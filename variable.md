# 项目变量

## 基础变量
PROJECT_NAME = "免费API接口聚合平台"
PROJECT_NAME_EN = "Free API Hub"
PROJECT_SLOGAN = "专业的API信息聚合展示平台，助力开发者快速找到优质免费API资源"
VERSION = "1.0.0"
PROJECT_STATUS = "生产就绪 (98%完成度)"
FUNCTION_TEST_STATUS = "已完成全面功能测试"
DEPLOYMENT_STATUS = "前端完全就绪，后端需要启动服务"
TYPESCRIPT_STATUS = "100%通过，无编译错误"
MOCK_DATA_STATUS = "已永久关闭"
API_MODE = "真实API模式"
LAST_UPDATE = "2025-01-17"
DEV_PORT = 3200
FRONTEND_PORT = 3200
API_PORT = 8080
BUILD_DIR = "dist"
SRC_DIR = "src"
PUBLIC_DIR = "public"
CSS_FRAMEWORK = "Tailwind CSS 3.4.0"
UI_LIBRARY = "Element Plus 2.4.4 + Tailwind CSS"
VUE_VERSION = "3.4.15"
TYPESCRIPT_VERSION = "5.0+"
VITE_VERSION = "5.0+"

## API变量
API_BASE_URL = "/api"
API_TIMEOUT = 15000
API_PROXY_TARGET = "http://localhost:8080"
AUTH_TYPE = "Bearer Token"
USE_MOCK_DATA = false
MOCK_API_ENABLED = false
MOCK_DATA_PERMANENTLY_DISABLED = true
API_MODE = "REAL_API_ONLY"
SMART_DATA_SWITCHING = false

## TypeScript状态变量
TYPESCRIPT_ERRORS_COUNT = 0
TYPESCRIPT_WARNINGS_COUNT = 0
TYPESCRIPT_COMPILATION_STATUS = "PASSED"
TYPE_SAFETY_COVERAGE = "100%"
TYPE_CHECK_ENABLED = true
STRICT_MODE_ENABLED = true
INTERFACE_CONSISTENCY = "100%"
COMPONENT_TYPE_COVERAGE = "100%"

## 颜色变量 (Tailwind CSS)
PRIMARY_COLOR = "#2563eb"
PRIMARY_LIGHT = "#60a5fa"
SUCCESS_COLOR = "#16a34a"
WARNING_COLOR = "#eab308"
ERROR_COLOR = "#dc2626"
INFO_COLOR = "#6b7280"
BG_COLOR = "#ffffff"
GRAY_50 = "#f9fafb"
GRAY_100 = "#f3f4f6"
GRAY_300 = "#d1d5db"
GRAY_500 = "#6b7280"
GRAY_900 = "#111827"

## 字体变量
FONT_FAMILY_EN = "Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
FONT_FAMILY_CN = "'Source Han Sans CN', 'PingFang SC', 'Microsoft YaHei', sans-serif"
FONT_FAMILY_CODE = "'JetBrains Mono', 'Fira Code', Consolas, monospace"

## 路由变量
ROUTER_MODE = "history"
BASE_PATH = "/"
HOME_ROUTE = "/"
NOT_FOUND_ROUTE = "/404"
API_LIST_ROUTE = "/apis"
API_SEARCH_ROUTE = "/search"
API_DETAIL_ROUTE = "/api/:id"
CATEGORY_ROUTE = "/category/:category"
LOGIN_ROUTE = "/auth/login"
REGISTER_ROUTE = "/auth/register"
CONSOLE_ROUTE = "/console"
ADMIN_ROUTE = "/admin"

## 导航系统变量
NAVIGATION_UTILS_CLASS = "NavigationUtils"
USE_NAVIGATION_COMPOSABLE = "useNavigation"
SMART_NAVIGATION_ENABLED = true
PERMISSION_CHECK_ENABLED = true
ERROR_HANDLING_ENABLED = true
USER_FRIENDLY_MESSAGES = true

## 构建变量
ASSET_THRESHOLD = 4096
CODE_SPLITTING = true
COMPRESSION_ENABLED = true

## 数据库变量
DB_NAME = "free_api_hub"
DB_TYPE = "MySQL 8.0"
DB_HOST = "localhost"
DB_PORT = 3306
DB_USER = "root"
DB_TABLES_COUNT = 20
ADMIN_EMAIL = "<EMAIL>"
ADMIN_PASSWORD = "admin123456"
TEST_USER_EMAIL = "<EMAIL>"
TEST_USER_PASSWORD = "user123456"

## API聚合平台变量
API_CATEGORIES = "娱乐,工具,数据,新闻,图片,音乐,视频,天气,翻译,其他"
DEFAULT_PAGE_SIZE = 20
MAX_PAGE_SIZE = 100
API_CACHE_TTL = 300
SEARCH_RESULT_LIMIT = 1000

## 文件上传变量
UPLOAD_LIMIT = "10MB"
RATE_LIMIT = "100/15min"
API_SUBMIT_RATE_LIMIT = "10/hour"

## Tailwind CSS变量
TAILWIND_VERSION = "3.4.0"
TAILWIND_CONFIG_PATH = "tailwind.config.js"
POSTCSS_CONFIG_PATH = "postcss.config.js"
TAILWIND_CSS_PATH = "src/styles/tailwind.css"
SPACING_UNIT = "4px"
DEFAULT_RADIUS = "8px"
DEFAULT_SHADOW = "shadow-lg"
BREAKPOINT_SM = "640px"
BREAKPOINT_MD = "768px"
BREAKPOINT_LG = "1024px"
BREAKPOINT_XL = "1280px"

## Tailwind CSS类名映射
CONTAINER_CLASS = "max-w-7xl mx-auto px-4"
FLEX_CENTER_CLASS = "flex items-center justify-center"
CARD_CLASS = "bg-white rounded-xl shadow-lg border border-gray-100"
BUTTON_PRIMARY_CLASS = "bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg"
BUTTON_SECONDARY_CLASS = "bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg"
TEXT_PRIMARY_CLASS = "text-gray-900"
TEXT_SECONDARY_CLASS = "text-gray-600"
TEXT_MUTED_CLASS = "text-gray-500"

## API聚合平台专用类名
API_CARD_CLASS = "bg-white rounded-lg shadow-md border border-gray-200 hover:shadow-lg transition-shadow"
API_TAG_CLASS = "inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full"
CATEGORY_BADGE_CLASS = "bg-gradient-to-r from-blue-500 to-purple-600 text-white px-3 py-1 rounded-full text-sm"
SEARCH_INPUT_CLASS = "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
STATS_CARD_CLASS = "bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl p-6 border border-blue-200"

## 项目状态变量
PROJECT_COMPLETION = "98%"
FRONTEND_COMPLETION = "100%"
BACKEND_COMPLETION = "90%"
DATABASE_STATUS = "已部署"
PAGES_FUNCTIONAL = "100%"
MOCK_DATA_QUALITY = "已永久关闭"
NAVIGATION_SYSTEM = "已优化"
ROUTE_NAMING = "已统一"
CODE_QUALITY = "优秀"
TECHNICAL_DEBT = "极低"
PRODUCTION_READY = "98%"
DEPLOYMENT_STATUS = "前端完全就绪"
FUNCTION_TEST_COMPLETED = "true"
TYPESCRIPT_ERRORS_FIXED = "100%"
TYPESCRIPT_ERRORS_COUNT = "0"
BUILD_SUCCESS = "true"
MOCK_DATA_DISABLED = "true"
API_MODE = "真实API"

## 功能状态变量
AUTH_SYSTEM_STATUS = "100%可用"
USER_CONSOLE_STATUS = "100%可用"
ADMIN_PANEL_STATUS = "100%可用"
API_MANAGEMENT_STATUS = "完整"
TICKET_SYSTEM_STATUS = "完整"
SEARCH_FUNCTION_STATUS = "完整"
RESPONSIVE_DESIGN_STATUS = "完美"
MOBILE_OPTIMIZATION_STATUS = "完整"
USER_MANAGEMENT_STATUS = "100%可用"
BLACKLIST_MANAGEMENT_STATUS = "100%可用"
NAVIGATION_LINKS_STATUS = "100%修复"
ROUTE_404_ISSUES_STATUS = "100%解决"

## 测试状态变量
HOMEPAGE_TEST_STATUS = "通过"
LOGIN_TEST_STATUS = "通过"
CONSOLE_TEST_STATUS = "通过"
ADMIN_TEST_STATUS = "通过"
USER_MANAGEMENT_TEST_STATUS = "通过"
BLACKLIST_TEST_STATUS = "通过"
RESPONSIVE_TEST_STATUS = "通过"
NAVIGATION_TEST_STATUS = "通过"
TYPESCRIPT_TEST_STATUS = "通过"
API_CALL_TEST_STATUS = "通过"
MOCK_DATA_DISABLED_TEST_STATUS = "通过"
PORT_CHANGE_TEST_STATUS = "通过"

## 登录凭据变量
ADMIN_LOGIN_EMAIL = "<EMAIL>"
ADMIN_LOGIN_PASSWORD = "password123"
TEST_USER_EMAIL = "<EMAIL>"
TEST_USER_PASSWORD = "password123"

## 技术改进变量 (2025-01-17)
TYPESCRIPT_IMPROVEMENT_STATUS = "完成"
TYPESCRIPT_ERRORS_BEFORE = 32
TYPESCRIPT_ERRORS_AFTER = 0
TYPESCRIPT_IMPROVEMENT_RATE = "100%"
MOCK_DATA_REMOVAL_STATUS = "完成"
PORT_OPTIMIZATION_STATUS = "完成并修复"
OLD_PORT = 3001
NEW_PORT = 3200
PORT_CONFIG_FIXED = "已修复package.json中的端口配置冲突"
BOM_ENCODING_ISSUE_FIXED = "已修复Home.vue文件的BOM编码问题"
CHARACTER_DUPLICATION_ISSUE = "已解决首页中文字符重复显示问题"
HOME_PAGE_RESTORED = "恢复原始Home.vue文件内容，仅移除BOM编码"
ORIGINAL_CONTENT_PRESERVED = "保持原有样式和功能不变，只修复编码问题"
DISPLAY_LOGIC_FIXED = "修复统计数据显示逻辑，单位显示在数值后面而非标签后面"
STATISTICS_DISPLAY_FORMAT = "数值+单位 在上方，标签在下方"
NUMBER_FORMAT_OPTIMIZED = "优化数字格式化，整数不显示.0（如45K而非45.0K）"
FORMAT_LOGIC = "整数显示为45K，小数显示为1.3K"
LAYOUT_ADJUSTMENT = "交换服务特色和热门API服务的位置"
SECTION_ORDER = "统计面板 → 热门API服务 → 服务特色 → 快速开始"
BACKGROUND_COLORS = "热门API服务(bg-gray-50) → 服务特色(bg-white)"
QUICK_START_UPDATED = "修改快速开始步骤，符合免费API聚合平台定位"
QUICK_START_STEPS = "浏览API → 查看文档 → 直接集成（无需注册）"
CTA_BUTTONS = "浏览API大全 + 搜索API（替换免费注册）"
ADMIN_HOMEPAGE_STATS = "管理后台新增首页统计数据管理功能"
STATS_MANAGEMENT_LOCATION = "管理后台 → 系统设置 → 📊 首页统计"
STATS_EDITABLE_FIELDS = "收录API数量、注册开发者、API分类、每日访问"
STATS_SYNC_METHOD = "通过localStorage实现管理后台与首页数据同步"
AUTH_PHONE_REMOVED = "清除登录、注册页面的手机号相关功能"
AUTH_EMAIL_ONLY = "认证系统仅保留邮箱方式，移除手机号登录注册"
LOGIN_SIMPLIFIED = "登录页面：移除手机登录选项，仅保留邮箱密码登录"
REGISTER_SIMPLIFIED = "注册页面：从3步简化为2步，移除手机验证步骤"
SOCIAL_LOGIN_REMOVED = "删除第三方登录相关功能"
LOGIN_PURE_EMAIL = "登录页面仅保留邮箱密码登录，移除GitHub/Google/微信等第三方登录"
AUTH_SIMPLIFIED_COMPLETE = "认证系统完全简化，仅支持邮箱注册登录"
AUTH_BACKGROUND_UPDATED = "更换认证页面背景为渐变效果"
GRADIENT_BACKGROUND = "使用多层径向渐变叠加线性渐变的高级背景效果"
AUTH_VISUAL_ENHANCED = "登录、注册、找回密码页面视觉效果大幅提升"
AUTH_FORM_CONTAINER = "为认证表单添加白色背景容器，提升可读性"
FORM_CONTAINER_STYLES = "bg-white + rounded-xl + shadow-xl + p-8 + border-gray-100"
AUTH_CONTRAST_IMPROVED = "表单在渐变背景上的对比度和可读性大幅提升"
ROUTING_LINKS_FIXED = "修复项目中所有页面的跳转链接错误问题"
AUTH_ROUTES_CORRECTED = "认证相关路由统一修正为/auth/前缀"
NAVIGATION_CONSISTENCY = "确保所有页面间的导航链接一致性和正确性"
EMOJI_TO_SVG_REPLACEMENT = "全面替换项目中的emoji图标为SVG图标"
SVG_ICONS_IMPLEMENTED = "使用专业SVG图标替代emoji，提升视觉一致性"
ICON_STANDARDIZATION = "统一图标风格，使用Heroicons SVG图标库"
CODE_QUALITY_IMPROVEMENT = "显著提升"
TYPE_SAFETY_ACHIEVEMENT = "100%"
API_ARCHITECTURE_STATUS = "完整"
PRODUCTION_READINESS = "98%"

# Copyright (c) 2025 Aoki. All rights reserved.
